اضافة بلاغ صيانة جديد #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة معاد هيكلتها من main_gui.py
تستخدم نافذة رئيسية واحدة مع إطارات لتسجيل الدخول والواجهة الرئيسية
"""

import tkinter as tk
from tkinter import messagebox
import ttkbootstrap as ttk_bs
from engineering_management_system import DatabaseManager, AuthenticationManager

try:
    from simple_project_management import SimpleProjectManagementWindow, SimpleProjectDialog
    PROJECT_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة المشاريع: {e}")
    PROJECT_MODULE_AVAILABLE = False

try:
    from reports_management import ReportsManagementWindow
    REPORTS_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة التقارير: {e}")
    REPORTS_MODULE_AVAILABLE = False

try:
    from buildings_management import BuildingsManagementWindow, BuildingDialog
    BUILDINGS_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة المباني والمرافق: {e}")
    BUILDINGS_MODULE_AVAILABLE = False

try:
    from maintenance_management import MaintenanceManagementWindow, MaintenanceDialog
    MAINTENANCE_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لا يمكن استيراد وحدة الصيانة: {e}")
    MAINTENANCE_MODULE_AVAILABLE = False

class LoginFrame(ttk_bs.Frame):
    def __init__(self, parent, auth_manager, on_login_success):
        super().__init__(parent, padding=20)
        self.auth_manager = auth_manager
        self.on_login_success = on_login_success
        self.setup_widgets()

    def setup_widgets(self):
        self.title_label = ttk_bs.Label(self, text="🏗️ تسجيل الدخول - نظام إدارة أعمال الإدارة الهندسية", font=("Segoe UI", 16, "bold"), bootstyle="primary")
        self.title_label.pack(pady=(0, 20))

        ttk_bs.Label(self, text="👤 اسم المستخدم", font=("Segoe UI", 12)).pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk_bs.Entry(self, width=30)
        self.username_entry.pack(pady=(0, 15))
        self.username_entry.insert(0, "admin")

        ttk_bs.Label(self, text="🔒 كلمة المرور", font=("Segoe UI", 12)).pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk_bs.Entry(self, width=30, show="*")
        self.password_entry.pack(pady=(0, 20))
        self.password_entry.insert(0, "admin123")

        btn_frame = ttk_bs.Frame(self)
        btn_frame.pack(fill=tk.X)

        login_btn = ttk_bs.Button(btn_frame, text="🚀 تسجيل الدخول", bootstyle="success", command=self.login)
        login_btn.pack(side=tk.RIGHT, padx=5)

        cancel_btn = ttk_bs.Button(btn_frame, text="❌ إلغاء", bootstyle="secondary", command=self.master.destroy)
        cancel_btn.pack(side=tk.RIGHT)

        self.username_entry.focus()
        self.master.bind('<Return>', lambda e: self.login())

    def login(self):
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        if self.auth_manager.authenticate(username, password):
            messagebox.showinfo("نجح", f"مرحباً {self.auth_manager.current_user['full_name']}")
            self.on_login_success()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

class MainApplication(ttk_bs.Window):
    def __init__(self):
        super().__init__(title="🏗️ نظام إدارة أعمال الإدارة الهندسية", themename="cosmo", size=(700, 650))
        self.db_manager = DatabaseManager("data/engineering_system.db")
        self.auth_manager = AuthenticationManager(self.db_manager)
        self.login_frame = LoginFrame(self, self.auth_manager, self.show_main_interface)
        self.login_frame.pack(fill=tk.BOTH, expand=True)
        self.main_frame = None

    def show_main_interface(self):
        self.login_frame.pack_forget()

        # تغيير حجم النافذة لتكون بحجم الشاشة
        self.state('zoomed')  # للويندوز
        # أو يمكن استخدام:
        # self.attributes('-zoomed', True)  # للينكس
        # self.attributes('-fullscreen', True)  # للشاشة الكاملة

        self.main_frame = ttk_bs.Frame(self, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        self.create_menu()
        self.create_main_interface()
        self.create_footer()
        self.update_footer()

        # إضافة متتبع للنوافذ المفتوحة
        self.open_windows = {}

    def show_or_focus_window(self, window_key, window_class, *args, **kwargs):
        """عرض النافذة أو إحضارها للمقدمة إذا كانت مفتوحة"""
        if window_key in self.open_windows:
            # التحقق من أن النافذة لا تزال موجودة
            try:
                if self.open_windows[window_key].window and self.open_windows[window_key].window.winfo_exists():
                    # إحضار النافذة للمقدمة
                    self.open_windows[window_key].window.lift()
                    self.open_windows[window_key].window.focus_force()
                    return
                else:
                    # النافذة مغلقة، إزالتها من القائمة
                    del self.open_windows[window_key]
            except:
                # النافذة مغلقة أو تالفة، إزالتها من القائمة
                del self.open_windows[window_key]

        # إنشاء نافذة جديدة
        window_instance = window_class(self, self.db_manager, self.auth_manager, *args, **kwargs)
        window_instance.show()
        self.open_windows[window_key] = window_instance

    def create_menu(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="إعدادات النظام", command=self.show_settings)
        file_menu.add_separator()
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_command(label="خروج", command=self.destroy)

        # قائمة المشاريع
        projects_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشاريع", menu=projects_menu)
        projects_menu.add_command(label="إدارة المشاريع", command=self.show_projects)
        projects_menu.add_command(label="مشروع جديد", command=self.new_project)
        projects_menu.add_command(label="تقارير المشاريع", command=self.show_project_reports)

        # قائمة المباني
        buildings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المباني والمرافق", menu=buildings_menu)
        buildings_menu.add_command(label="إدارة المباني", command=self.show_buildings)
        buildings_menu.add_command(label="مبنى جديد", command=self.new_building)

        # قائمة الصيانة
        maintenance_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الصيانة", menu=maintenance_menu)
        maintenance_menu.add_command(label="بلاغات الأعطال", command=self.show_maintenance_requests)

        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="لوحة التحكم", command=self.show_dashboard)
        reports_menu.add_command(label="تقارير المشاريع", command=self.show_project_reports)
        reports_menu.add_command(label="تقارير الصيانة", command=self.show_maintenance_reports)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.show_help)
        help_menu.add_command(label="حول النظام", command=self.show_about)

    def create_main_interface(self):
        # إنشاء دفتر الملاحظات (Notebook) للتبويبات المحسن
        self.notebook = ttk_bs.Notebook(self.main_frame, bootstyle="primary")
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # تطبيق خط مخصص للتبويبات
        style = ttk_bs.Style()
        style.configure("TNotebook.Tab",
                        font=("Segoe UI", 12),
                        foreground="#1e3a8a",
                        padding=[20, 10])

        # تبويب لوحة التحكم
        self.dashboard_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.dashboard_frame, text="🏠 لوحة التحكم")

        # تبويب المشاريع
        self.projects_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.projects_frame, text="🏗️ المشاريع")

        # تبويب المباني
        self.buildings_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.buildings_frame, text="🏢 المباني والمرافق")

        # تبويب الصيانة
        self.maintenance_frame = ttk_bs.Frame(self.notebook)
        self.notebook.add(self.maintenance_frame, text="🔧 الصيانة")

        # إنشاء محتوى التبويبات
        self.create_dashboard()
        self.create_projects_tab()
        self.create_buildings_tab()
        self.create_maintenance_tab()

    def create_footer(self):
        self.footer = ttk_bs.Frame(self)
        self.footer.pack(side=tk.BOTTOM, fill=tk.X)

        content_frame = ttk_bs.Frame(self.footer)
        content_frame.pack(fill=tk.X, padx=15, pady=12)

        user_name = self.auth_manager.current_user.get('full_name', 'غير محدد')
        self.user_label = ttk_bs.Label(content_frame, text=f"👤 المستخدم {user_name}", font=("Segoe UI", 10, "bold"), bootstyle="primary")
        self.user_label.pack(side=tk.LEFT)

        program_label = ttk_bs.Label(content_frame, text="🏗️ نظام إدارة أعمال الإدارة الهندسية © 2025 🏗️", font=("Segoe UI", 10, "bold"), bootstyle="primary")
        program_label.pack(expand=True)

        self.time_label = ttk_bs.Label(content_frame, text="", font=("Segoe UI", 10, "bold"), bootstyle="primary")
        self.time_label.pack(side=tk.RIGHT)

        # خط فاصل
        try:
            separator = ttk_bs.Separator(self.footer, orient='horizontal')
            separator.pack(fill=tk.X, side=tk.TOP)
        except Exception as e:
            print(f"⚠️ تحذير: لا يمكن إنشاء الخط الفاصل: {e}")

    def update_footer(self):
        import datetime
        now = datetime.datetime.now()
        date_text = now.strftime("%Y/%m/%d")
        time_text = now.strftime("%H:%M:%S")
        full_text = f"📅 {date_text} ⏰ {time_text}"
        if hasattr(self, 'time_label') and self.time_label.winfo_exists():
            self.time_label.config(text=full_text)
        self.after(1000, self.update_footer)

    def create_dashboard(self):
        title_frame = ttk_bs.Frame(self.dashboard_frame)
        title_frame.pack(fill=tk.X, pady=20)

        title_label = ttk_bs.Label(title_frame, text="🏗️ لوحة التحكم الرئيسية 🏗️", font=("Segoe UI", 16, "bold"), bootstyle="primary", foreground="#1e3a8a")
        title_label.pack()

        subtitle_label = ttk_bs.Label(title_frame, text="مرحباً بك في نظام إدارة أعمال الإدارة الهندسية", font=("Segoe UI", 12), bootstyle="secondary")
        subtitle_label.pack(pady=(5, 0))

        stats_frame = ttk_bs.LabelFrame(self.dashboard_frame, text="📊 الإحصائيات السريعة", padding=15, bootstyle="info")
        stats_frame.pack(fill=tk.X, padx=15, pady=10)

        stats_row1 = ttk_bs.Frame(stats_frame)
        stats_row1.pack(fill=tk.X, pady=10)

        self.create_stat_card(stats_row1, "🏗️ المشاريع النشطة", "0", "success")
        self.create_stat_card(stats_row1, "⚠️ البلاغات المفتوحة", "0", "warning")
        self.create_stat_card(stats_row1, "🏢 المباني المسجلة", "0", "info")
        self.create_stat_card(stats_row1, "🔧 أعمال الصيانة", "0", "secondary")

        notifications_frame = ttk_bs.LabelFrame(self.dashboard_frame, text="🔔 الإشعارات والتنبيهات", padding=15, bootstyle="warning")
        notifications_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        self.notifications_tree = ttk_bs.Treeview(notifications_frame, columns=("type", "message", "date"), show="headings", height=10)
        self.notifications_tree.heading("type", text="📋 النوع")
        self.notifications_tree.heading("message", text="💬 الرسالة")
        self.notifications_tree.heading("date", text="📅 التاريخ")

        self.notifications_tree.column("type", width=120)
        self.notifications_tree.column("message", width=500)
        self.notifications_tree.column("date", width=180)

        notifications_scrollbar = tk.Scrollbar(notifications_frame, orient="vertical", command=self.notifications_tree.yview)
        self.notifications_tree.configure(yscrollcommand=notifications_scrollbar.set)

        self.notifications_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        notifications_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.update_dashboard_stats()

    def create_stat_card(self, parent, title, value, style):
        card_frame = ttk_bs.LabelFrame(parent, text="", padding=15, bootstyle=style)
        card_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=8, pady=5)

        title_label = ttk_bs.Label(card_frame, text=title, font=("Segoe UI", 12), bootstyle=style, foreground="#1e3a8a")
        title_label.pack(pady=(0, 8))

        value_label = ttk_bs.Label(card_frame, text=value, font=("Segoe UI", 24, "bold"), bootstyle=style, foreground="#1e3a8a")
        value_label.pack()

    def update_dashboard_stats(self):
        if hasattr(self, 'notifications_tree'):
            for item in self.notifications_tree.get_children():
                self.notifications_tree.delete(item)

            sample_notifications = [
                ("معلومات", "مرحباً بك في النظام المحسن! 🎉", "2024/01/15 - 10:30"),
                ("تحديث", "تم تحسين واجهة المستخدم بنجاح ✨", "2024/01/15 - 10:25"),
                ("نجاح", "جميع الشاشات تفتح الآن في وسط الشاشة 🎯", "2024/01/15 - 10:20"),
                ("تحسين", "تم تكبير الخطوط وتحسين الألوان 🎨", "2024/01/15 - 10:15"),
                ("ميزة", "تذييل محسن مع الوقت والتاريخ ⏰", "2024/01/15 - 10:10")
            ]

            for notification in sample_notifications:
                self.notifications_tree.insert('', 'end', values=notification)

    def create_projects_tab(self):
        title_frame = ttk_bs.Frame(self.projects_frame)
        title_frame.pack(fill=tk.X, pady=20)

        title_label = ttk_bs.Label(title_frame, text="🏗️ إدارة المشاريع الهندسية 🏗️", font=("Segoe UI", 16, "bold"), bootstyle="primary", foreground="#1e3a8a")
        title_label.pack()

        buttons_frame = ttk_bs.LabelFrame(self.projects_frame, text="🛠️ أدوات المشاريع السريعة", padding=20, bootstyle="info")
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        btn_row = ttk_bs.Frame(buttons_frame)
        btn_row.pack(fill=tk.X, pady=10)

        ttk_bs.Button(btn_row, text="📋 عرض جميع المشاريع", command=self.show_projects, bootstyle="primary", width=25).pack(side=tk.LEFT, padx=10, ipady=10)
        ttk_bs.Button(btn_row, text="➕ إضافة مشروع جديد", command=self.new_project, bootstyle="success", width=25).pack(side=tk.LEFT, padx=10, ipady=10)
        ttk_bs.Button(btn_row, text="📊 تقارير المشاريع", command=self.show_project_reports, bootstyle="warning", width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        info_frame = ttk_bs.LabelFrame(self.projects_frame, text="📈 معلومات المشاريع", padding=20, bootstyle="secondary")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        info_text = """
        🏗️ نظام إدارة المشاريع الهندسية يوفر

        ✅ إدارة شاملة لجميع المشاريع
        ✅ تتبع حالة المشاريع ونسب الإنجاز
        ✅ إدارة المقاولين والتكاليف
        ✅ تقارير مفصلة عن أداء المشاريع
        ✅ واجهة سهلة الاستخدام ومحسنة
        """

        info_label = ttk_bs.Label(info_frame, text=info_text, font=("Segoe UI", 12), foreground="#1e3a8a", justify=tk.LEFT)
        info_label.pack(anchor=tk.W)

    def create_buildings_tab(self):
        title_frame = ttk_bs.Frame(self.buildings_frame)
        title_frame.pack(fill=tk.X, pady=20)

        title_label = ttk_bs.Label(title_frame, text="🏢 إدارة المباني والمرافق 🏢", font=("Segoe UI", 16, "bold"), bootstyle="primary", foreground="#1e3a8a")
        title_label.pack()

        dev_frame = ttk_bs.LabelFrame(self.buildings_frame, text="🚧 قيد التطوير", padding=30, bootstyle="warning")
        dev_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        dev_text = """
        🏢 وحدة إدارة المباني والمرافق قيد التطوير

        ستتضمن الميزات التالية
        🏗️ تسجيل وإدارة المباني
        📋 متابعة حالة المرافق
        🔧 جدولة أعمال الصيانة
        📊 تقارير حالة المباني
        """

        dev_label = ttk_bs.Label(dev_frame, text=dev_text, font=("Segoe UI", 14), foreground="#1e3a8a", justify=tk.CENTER)
        dev_label.pack(expand=True)

    def create_maintenance_tab(self):
        title_frame = ttk_bs.Frame(self.maintenance_frame)
        title_frame.pack(fill=tk.X, pady=20)

        title_label = ttk_bs.Label(title_frame, text="🔧 إدارة أعمال الصيانة 🔧", font=("Segoe UI", 16, "bold"), bootstyle="primary", foreground="#1e3a8a")
        title_label.pack()

        buttons_frame = ttk_bs.LabelFrame(self.maintenance_frame, text="�️ أدوات الصيانة السريعة", padding=20, bootstyle="info")
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        btn_row = ttk_bs.Frame(buttons_frame)
        btn_row.pack(fill=tk.X, pady=10)

        ttk_bs.Button(btn_row, text="📋 بلاغات الأعطال", command=self.show_maintenance_requests, bootstyle="primary", width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        info_frame = ttk_bs.LabelFrame(self.maintenance_frame, text="📈 معلومات الصيانة", padding=20, bootstyle="secondary")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        info_text = """
        🔧 نظام إدارة الصيانة يوفر

        ✅ إدارة شاملة لبلاغات الأعطال
        ✅ تتبع حالة البلاغات ومستوى الأولوية
        ✅ إسناد المهام للفنيين المختصين
        ✅ واجهة سهلة الاستخدام ومحسنة
        ✅ تسجيل مفصل لجميع أعمال الصيانة
        """

        info_label = ttk_bs.Label(info_frame, text=info_text, font=("Segoe UI", 12), foreground="#1e3a8a", justify=tk.LEFT)
        info_label.pack(anchor=tk.W)

    # وظائف القوائم
    def show_settings(self):
        messagebox.showinfo("إعدادات النظام", "هذه الميزة قيد التطوير.")

    def logout(self):
        # إغلاق جميع النوافذ المفتوحة
        for window_key, window_instance in list(self.open_windows.items()):
            try:
                if window_instance.window and window_instance.window.winfo_exists():
                    window_instance.window.destroy()
            except:
                pass
        self.open_windows.clear()

        # العودة لشاشة تسجيل الدخول
        self.main_frame.pack_forget()
        self.main_frame.destroy()
        self.main_frame = None
        self.auth_manager.logout()

        # إعادة تعيين حجم النافذة لتسجيل الدخول
        self.state('normal')
        self.geometry("700x650")

        self.login_frame = LoginFrame(self, self.auth_manager, self.show_main_interface)
        self.login_frame.pack(fill=tk.BOTH, expand=True)

    def show_projects(self):
        if PROJECT_MODULE_AVAILABLE:
            self.show_or_focus_window("projects", SimpleProjectManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المشاريع غير متوفرة")

    def new_project(self):
        if PROJECT_MODULE_AVAILABLE:
            dialog = SimpleProjectDialog(self, self.db_manager, self.auth_manager, "إضافة مشروع جديد")
            dialog.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المشاريع غير متوفرة")

    def show_buildings(self):
        if BUILDINGS_MODULE_AVAILABLE:
            self.show_or_focus_window("buildings", BuildingsManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المباني غير متوفرة")

    def new_building(self):
        if BUILDINGS_MODULE_AVAILABLE:
            dialog = BuildingDialog(self, self.db_manager, self.auth_manager, "إضافة مبنى جديد")
            dialog.show()
        else:
            messagebox.showerror("خطأ", "وحدة إدارة المباني غير متوفرة")

    def show_maintenance_requests(self):
        if MAINTENANCE_MODULE_AVAILABLE:
            self.show_or_focus_window("maintenance", MaintenanceManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة إدارة الصيانة غير متوفرة")



    def show_dashboard(self):
        if REPORTS_MODULE_AVAILABLE:
            self.show_or_focus_window("reports", ReportsManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")

    def show_project_reports(self):
        if REPORTS_MODULE_AVAILABLE:
            self.show_or_focus_window("project_reports", ReportsManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")

    def show_maintenance_reports(self):
        if REPORTS_MODULE_AVAILABLE:
            self.show_or_focus_window("maintenance_reports", ReportsManagementWindow)
        else:
            messagebox.showerror("خطأ", "وحدة التقارير غير متوفرة")

    def show_help(self):
        messagebox.showinfo("مساعدة", "دليل المستخدم قيد التطوير.")

    def show_about(self):
        about_text = """
🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن 🏗️

📋 الإصدار: 2.0 Enhanced
🎨 التحسينات الجديدة:
   ✅ جميع الشاشات تفتح في وسط الشاشة
   ✅ خطوط أكبر وأوضح
   ✅ ألوان وأشكال مميزة
   ✅ تذييل محسن مع الوقت والتاريخ
   ✅ خط أزرق غامق وثقيل
   ✅ واجهة مستخدم محسنة بالكامل

👨‍💻 تطوير فريق التطوير المتخصص
📅 تاريخ التحديث 2024/01/15
🎯 الهدف تحسين تجربة المستخدم
        """
        messagebox.showinfo("🏗️ حول النظام المحسن", about_text)

    def logout(self):
        self.main_frame.pack_forget()
        self.main_frame.destroy()
        self.main_frame = None
        self.auth_manager.logout()
        self.login_frame = LoginFrame(self, self.auth_manager, self.show_main_interface)
        self.login_frame.pack(fill=tk.BOTH, expand=True)

if __name__ == "__main__":
    app = MainApplication()
    app.mainloop()
