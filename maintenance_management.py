"""
وحدة إدارة الصيانة
Maintenance Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs

class MaintenanceManagementWindow:
    """نافذة إدارة الصيانة"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.maintenance_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def setup_arabic_pdf_support(self):
        """إعداد دعم اللغة العربية في PDF مع خطوط متعددة محسنة"""
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import os
            import platform

            self.registered_fonts = {}

            # قائمة الخطوط العربية المدعومة حسب نظام التشغيل
            if platform.system() == "Windows":
                font_paths = {
                    'Arabic-Bold': [
                        "C:/Windows/Fonts/arialbd.ttf",
                        "C:/Windows/Fonts/tahomabd.ttf",
                        "C:/Windows/Fonts/calibrib.ttf"
                    ],
                    'Arabic-Regular': [
                        "C:/Windows/Fonts/arial.ttf",
                        "C:/Windows/Fonts/tahoma.ttf",
                        "C:/Windows/Fonts/calibri.ttf",
                        "C:/Windows/Fonts/segoeui.ttf"
                    ],
                    'Arabic-Italic': [
                        "C:/Windows/Fonts/ariali.ttf",
                        "C:/Windows/Fonts/calibrii.ttf"
                    ]
                }
            else:
                # مسارات الخطوط في لينكس/ماك
                font_paths = {
                    'Arabic-Bold': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                        "/System/Library/Fonts/Arial Bold.ttf"
                    ],
                    'Arabic-Regular': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/System/Library/Fonts/Arial.ttf"
                    ],
                    'Arabic-Italic': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Oblique.ttf"
                    ]
                }

            # تسجيل الخطوط المتوفرة
            for font_name, paths in font_paths.items():
                for font_path in paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            self.registered_fonts[font_name] = font_name
                            print(f"✅ تم تسجيل الخط: {font_name} من {font_path}")
                            break
                        except Exception as e:
                            print(f"⚠️ فشل في تسجيل {font_name}: {e}")
                            continue

            # التأكد من وجود خط أساسي على الأقل
            if not self.registered_fonts:
                print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
                # إضافة خطوط احتياطية
                self.registered_fonts = {
                    'Arabic-Bold': 'Helvetica-Bold',
                    'Arabic-Regular': 'Helvetica',
                    'Arabic-Italic': 'Helvetica-Oblique'
                }
                return False

            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد الخطوط العربية: {e}")
            # إضافة خطوط احتياطية في حالة الخطأ
            self.registered_fonts = {
                'Arabic-Bold': 'Helvetica-Bold',
                'Arabic-Regular': 'Helvetica',
                'Arabic-Italic': 'Helvetica-Oblique'
            }
            return False

    def show(self):
        """عرض نافذة إدارة الصيانة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 إدارة الصيانة")
        self.window.geometry("1100x650")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_maintenance_requests()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة الصيانة"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🔧 إدارة الصيانة والأعطال 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات الصيانة",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار
        ttk_bs.Button(
            toolbar_frame,
            text="➕ بلاغ جديد",
            command=self.add_maintenance_request,
            bootstyle="success",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_maintenance_request,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✅ إنجاز",
            command=self.complete_maintenance,
            bootstyle="success",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_maintenance_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_maintenance_requests,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🖨️ طباعة PDF",
            command=self.print_maintenance_report,
            bootstyle="primary",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث والفلترة
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث:"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=30,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_maintenance)

        ttk_bs.Label(
            search_frame,
            text="📊 الحالة:"
        ).pack(side=tk.LEFT, padx=(20, 5))

        self.status_filter = ttk_bs.Combobox(
            search_frame,
            width=15,
            bootstyle="warning"
        )
        self.status_filter['values'] = ("الكل", "جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
        self.status_filter.set("الكل")
        self.status_filter.pack(side=tk.LEFT, padx=5, ipady=3)
        self.status_filter.bind('<<ComboboxSelected>>', self.filter_maintenance)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_maintenance,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول بلاغات الصيانة
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔧 بلاغات الصيانة والأعطال",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview
        columns = ("id", "title", "building_id", "priority", "status", "reported_by", "assigned_to", "created_at", "notes")
        self.maintenance_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تطبيق خط على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")
        
        # تعريف العناوين
        headers = {
            "id": "🆔 المعرف",
            "title": "📝 عنوان البلاغ",
            "building_id": "🏢 معرف المبنى",
            "priority": "⚡ الأولوية",
            "status": "📊 الحالة",
            "reported_by": "👤 المبلغ",
            "assigned_to": "👷 المكلف",
            "created_at": "📅 تاريخ الإنشاء",
            "notes": "📝 ملاحظات"
        }
        
        for col, header in headers.items():
            self.maintenance_tree.heading(col, text=header)
            if col == "id":
                self.maintenance_tree.column(col, width=50)
            elif col in ["title", "building_id"]:
                self.maintenance_tree.column(col, width=150)
            else:
                self.maintenance_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.maintenance_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.maintenance_tree.xview)
        self.maintenance_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.maintenance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.maintenance_tree.bind('<Double-1>', lambda e: self.view_maintenance_details())
    
    def load_maintenance_requests(self):
        """تحميل بلاغات الصيانة من قاعدة البيانات"""
        if not self.maintenance_tree:
            return

        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)

        try:
            # قراءة البيانات من قاعدة البيانات
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الأعمدة الجديدة أولاً
            cursor.execute("PRAGMA table_info(maintenance_requests)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'department_name' in columns and 'section_name' in columns:
                cursor.execute('''
                    SELECT id, title, building_id, priority, status, reported_by,
                           assigned_to, created_at, notes, department_name, section_name
                    FROM maintenance_requests
                    ORDER BY created_at DESC
                ''')
            else:
                cursor.execute('''
                    SELECT id, title, building_id, priority, status, reported_by,
                           assigned_to, created_at, notes
                    FROM maintenance_requests
                    ORDER BY created_at DESC
                ''')

            requests = cursor.fetchall()

            # خرائط التحويل من الإنجليزية إلى العربية للعرض
            priority_en_to_ar = {
                "urgent": "عالية",
                "high": "عالية",
                "medium": "متوسطة",
                "low": "منخفضة"
            }

            status_en_to_ar = {
                "open": "جديد",
                "assigned": "مؤجل",
                "in_progress": "قيد التنفيذ",
                "completed": "مكتمل",
                "closed": "مغلق"
            }

            for request in requests:
                # تحويل القيم للعرض
                display_request = list(request)

                # تحويل الأولوية
                if request[3]:  # priority
                    display_request[3] = priority_en_to_ar.get(request[3], request[3])

                # تحويل الحالة
                if request[4]:  # status
                    display_request[4] = status_en_to_ar.get(request[4], request[4])

                # تنسيق التاريخ
                if request[7]:  # created_at
                    try:
                        from datetime import datetime
                        if isinstance(request[7], str):
                            dt = datetime.fromisoformat(request[7].replace('Z', '+00:00'))
                            display_request[7] = dt.strftime('%Y/%m/%d %H:%M')
                        else:
                            display_request[7] = str(request[7])
                    except:
                        display_request[7] = str(request[7])

                # إضافة البيانات للجدول
                self.maintenance_tree.insert('', 'end', values=display_request)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بلاغات الصيانة: {e}")
            # في حالة الخطأ، عرض بيانات تجريبية محسنة
            self.load_sample_maintenance()

    def load_sample_maintenance(self):
        """تحميل بيانات وهمية لبلاغات الصيانة للعرض"""
        sample_requests = [
            (1, "عطل في نظام التكييف المركزي", "المبنى الإداري", "عالية", "قيد التنفيذ", "أحمد محمد", "فني التكييف", "2025/07/01 09:30", "يحتاج إلى قطع غيار"),
            (2, "تسريب في شبكة السباكة", "مبنى الهندسة", "عالية", "جديد", "سارة أحمد", "فني السباكة", "2025/07/02 14:15", "تسريب في الطابق الثاني"),
            (3, "مشكلة في الإضاءة", "مبنى المختبرات", "متوسطة", "مكتمل", "محمد علي", "فني الكهرباء", "2025/06/28 11:20", "تم استبدال المصابيح"),
            (4, "صيانة دورية للمصاعد", "جميع المباني", "منخفضة", "مجدول", "إدارة الصيانة", "شركة المصاعد", "2025/07/03 08:00", "صيانة شهرية"),
            (5, "إصلاح الباب الرئيسي", "المبنى الإداري", "متوسطة", "قيد التنفيذ", "خالد أحمد", "فني الأقفال", "2025/06/30 16:45", "مشكلة في آلية الإغلاق"),
            (6, "تنظيف أنظمة التهوية", "مبنى المكتبة", "منخفضة", "مكتمل", "فاطمة سالم", "فريق التنظيف", "2025/06/25 10:00", "تنظيف دوري مكتمل"),
            (7, "فحص أنظمة الإنذار", "مبنى الأمن", "عالية", "جديد", "عبدالله محمد", "فني الأمان", "2025/07/03 13:30", "فحص عاجل مطلوب"),
            (8, "صيانة الحدائق والمساحات الخضراء", "المنطقة الخارجية", "منخفضة", "قيد التنفيذ", "نورا عبدالله", "فريق البستنة", "2025/07/01 07:00", "صيانة أسبوعية")
        ]

        for request in sample_requests:
            self.maintenance_tree.insert('', 'end', values=request)
    
    def filter_maintenance(self, event=None):
        """فلترة بلاغات الصيانة"""
        search_text = self.search_entry.get().lower()
        status_filter = self.status_filter.get()
        
        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)
        
        # إعادة تحميل البيانات مع الفلترة
        self.load_maintenance_requests()

        # تطبيق الفلترة على البيانات المحملة
        if search_text or status_filter != "الكل":
            # جمع جميع العناصر
            all_items = []
            for item in self.maintenance_tree.get_children():
                values = self.maintenance_tree.item(item)['values']
                all_items.append((item, values))

            # مسح الجدول
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)

            # إعادة إدراج العناصر المطابقة للفلتر
            for item, values in all_items:
                # فلترة حسب النص
                text_match = (not search_text or
                             search_text in str(values[1]).lower() or
                             search_text in str(values[5]).lower() or
                             search_text in str(values[8]).lower())

                # فلترة حسب الحالة
                status_match = (status_filter == "الكل" or status_filter == values[4])

                if text_match and status_match:
                    self.maintenance_tree.insert('', 'end', values=values)
    
    def add_maintenance_request(self):
        """إضافة بلاغ صيانة جديد"""
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "إضافة بلاغ صيانة جديد")
        if dialog.show():
            self.load_maintenance_requests()
    
    def edit_maintenance_request(self):
        """تعديل بلاغ صيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ للتعديل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "تعديل بلاغ الصيانة", request_data)
        if dialog.show():
            self.load_maintenance_requests()
    
    def complete_maintenance(self):
        """إنجاز بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لإنجازه")
            return
        
        request_title = self.maintenance_tree.item(selected[0])['values'][1]
        
        if messagebox.askyesno("تأكيد الإنجاز", f"هل تم إنجاز '{request_title}' بنجاح؟"):
            messagebox.showinfo("نجح", "تم تحديث حالة البلاغ إلى 'مكتمل'")
            self.load_maintenance_requests()
    
    def view_maintenance_details(self):
        """عرض تفاصيل بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لعرض التفاصيل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        details = f"""
🔧 تفاصيل بلاغ الصيانة:

🆔 المعرف: {request_data[0]}
📝 العنوان: {request_data[1]}
🏢 المبنى: {request_data[2]}
🔧 نوع العطل: {request_data[3]}
⚡ الأولوية: {request_data[4]}
📊 الحالة: {request_data[5]}
👤 المبلغ: {request_data[6]}
📅 التاريخ: {request_data[7]}
👷 المكلف: {request_data[8]}
        """
        messagebox.showinfo("تفاصيل بلاغ الصيانة", details)

    def print_maintenance_report(self):
        """طباعة تقرير الصيانة والأعطال إلى PDF"""
        try:
            # التحقق من وجود مكتبة reportlab
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import cm
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
                from reportlab.lib import colors
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT
                from datetime import datetime
                import os
                from tkinter import filedialog
            except ImportError:
                messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها أولاً:\npip install reportlab")
                return

            # الحصول على بيانات الصيانة
            maintenance_data = self.get_maintenance_data_for_report()

            if not maintenance_data:
                messagebox.showwarning("تحذير", "لا توجد بلاغات صيانة لطباعتها")
                return

            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_الصيانة_{current_date}.pdf"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                initialfile=default_filename,
                title="حفظ تقرير الصيانة"
            )

            if not file_path:
                return

            # إنشاء ملف PDF
            self.create_maintenance_pdf_report(maintenance_data, file_path)

            messagebox.showinfo("نجح", f"تم إنشاء تقرير الصيانة بنجاح:\n{file_path}")

            # فتح الملف للطباعة بطريقة آمنة
            try:
                import platform
                import subprocess

                system = platform.system()
                if system == "Windows":
                    # استخدام طريقة آمنة لفتح الملف
                    try:
                        # محاولة فتح الملف مع برنامج PDF الافتراضي
                        subprocess.run(['start', '', file_path], shell=True, check=False)
                    except:
                        # في حالة فشل الطريقة الأولى، استخدم os.startfile
                        try:
                            os.startfile(file_path)
                        except:
                            # إذا فشلت جميع الطرق، أظهر رسالة للمستخدم
                            messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', file_path], check=False)
                elif system == "Linux":
                    subprocess.run(['xdg-open', file_path], check=False)
                else:
                    messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")
            except Exception as e:
                messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def get_maintenance_data_for_report(self):
        """الحصول على بيانات الصيانة للتقرير"""
        # إرجاع البيانات الوهمية المحسنة
        return [
            {
                'title': 'عطل في نظام التكييف المركزي',
                'building': 'المبنى الإداري',
                'priority': 'عالية',
                'status': 'قيد التنفيذ',
                'assigned_to': 'فني التكييف',
                'reported_date': '2025/07/01'
            },
            {
                'title': 'تسريب في شبكة السباكة',
                'building': 'مبنى الهندسة',
                'priority': 'عالية',
                'status': 'جديد',
                'assigned_to': 'فني السباكة',
                'reported_date': '2025/07/02'
            },
            {
                'title': 'مشكلة في الإضاءة',
                'building': 'مبنى المختبرات',
                'priority': 'متوسطة',
                'status': 'مكتمل',
                'assigned_to': 'فني الكهرباء',
                'reported_date': '2025/06/28'
            },
            {
                'title': 'صيانة دورية للمصاعد',
                'building': 'جميع المباني',
                'priority': 'منخفضة',
                'status': 'مجدول',
                'assigned_to': 'شركة المصاعد',
                'reported_date': '2025/07/03'
            },
            {
                'title': 'إصلاح الباب الرئيسي',
                'building': 'المبنى الإداري',
                'priority': 'متوسطة',
                'status': 'قيد التنفيذ',
                'assigned_to': 'فني الأقفال',
                'reported_date': '2025/06/30'
            }
        ]

    def create_maintenance_pdf_report(self, maintenance_data, file_path):
        """إنشاء تقرير PDF للصيانة"""
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from datetime import datetime

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        # إنشاء الأنماط
        styles = getSampleStyleSheet()

        # نمط العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        # نمط العنوان الفرعي
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            alignment=TA_RIGHT,
            textColor=colors.darkgreen
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي
        story.append(Paragraph("🔧 تقرير الصيانة والأعطال 🔧", title_style))
        story.append(Spacer(1, 20))

        # التاريخ
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        story.append(Paragraph(f"تاريخ التقرير: {current_time}", styles['Normal']))
        story.append(Spacer(1, 20))

        # إحصائيات سريعة
        total_requests = len(maintenance_data)
        completed_requests = len([r for r in maintenance_data if r['status'] == 'مكتمل'])
        pending_requests = len([r for r in maintenance_data if r['status'] in ['جديد', 'قيد التنفيذ']])
        high_priority = len([r for r in maintenance_data if r['priority'] == 'عالية'])

        story.append(Paragraph("📊 إحصائيات الصيانة", heading_style))
        story.append(Paragraph(f"• إجمالي البلاغات: {total_requests}", styles['Normal']))
        story.append(Paragraph(f"• البلاغات المكتملة: {completed_requests}", styles['Normal']))
        story.append(Paragraph(f"• البلاغات المعلقة: {pending_requests}", styles['Normal']))
        story.append(Paragraph(f"• البلاغات عالية الأولوية: {high_priority}", styles['Normal']))
        story.append(Spacer(1, 20))

        # جدول بلاغات الصيانة
        story.append(Paragraph("📋 تفاصيل بلاغات الصيانة", heading_style))

        # إنشاء بيانات الجدول
        table_data = [['العنوان', 'المبنى', 'الأولوية', 'الحالة', 'المسؤول']]

        for request in maintenance_data:
            table_data.append([
                request['title'][:30] + '...' if len(request['title']) > 30 else request['title'],
                request['building'],
                request['priority'],
                request['status'],
                request['assigned_to']
            ])

        # إنشاء الجدول
        table = Table(table_data, repeatRows=1)
        table.setStyle(TableStyle([
            # تنسيق الرأس
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

            # تنسيق البيانات
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        story.append(table)

        # بناء المستند
        doc.build(story)


class MaintenanceDialog:
    """نافذة إضافة/تعديل بلاغ صيانة"""
    
    def __init__(self, parent, db_manager, auth_manager, title, request_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.request_data = request_data
        self.result = False

        # خرائط التحويل بين العربية والإنجليزية
        self.priority_ar_to_en = {
            "عالية": "urgent",
            "متوسطة": "medium",
            "منخفضة": "low"
        }
        self.priority_en_to_ar = {v: k for k, v in self.priority_ar_to_en.items()}

        self.status_ar_to_en = {
            "جديد": "open",
            "قيد التنفيذ": "in_progress",
            "مكتمل": "completed",
            "مؤجل": "assigned"
        }
        self.status_en_to_ar = {v: k for k, v in self.status_ar_to_en.items()}
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.label_font = ("Segoe UI", 11)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("700x650")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # تفعيل زر الحفظ عند عرض النافذة
        if hasattr(self, 'save_button'):
            self.save_button.config(state="normal")

        # توسيط النافذة
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

        self.window.wait_window()
        return self.result

    def load_buildings_data(self, combo):
        """تحميل بيانات المباني من قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود الأعمدة الجديدة
            cursor.execute("PRAGMA table_info(buildings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'department_name' in columns and 'section_name' in columns:
                cursor.execute('''
                    SELECT id, name, department_name, section_name
                    FROM buildings
                    ORDER BY name
                ''')
            else:
                cursor.execute('''
                    SELECT id, name, '', ''
                    FROM buildings
                    ORDER BY name
                ''')

            buildings = cursor.fetchall()

            # تنسيق البيانات للعرض
            building_values = []
            self.buildings_data = {}  # لحفظ البيانات للاستخدام لاحقاً

            for building in buildings:
                building_id, name, department, section = building
                display_text = f"{building_id} - {name}"
                building_values.append(display_text)
                self.buildings_data[display_text] = {
                    'id': building_id,
                    'name': name,
                    'department': department or '',
                    'section': section or ''
                }

            combo['values'] = building_values

        except Exception as e:
            print(f"خطأ في تحميل بيانات المباني: {e}")
            # بيانات افتراضية في حالة الخطأ
            combo['values'] = ("1 - المبنى الإداري", "2 - مبنى الهندسة", "3 - مبنى المختبرات")
        finally:
            conn.close()

    def load_departments_data(self, combo):
        """تحميل قائمة الإدارات الفريدة من جدول المباني"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود عمود department_name
            cursor.execute("PRAGMA table_info(buildings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'department_name' in columns:
                cursor.execute('''
                    SELECT DISTINCT department_name
                    FROM buildings
                    WHERE department_name IS NOT NULL AND department_name != ''
                    ORDER BY department_name
                ''')
                departments = cursor.fetchall()
                department_values = [dept[0] for dept in departments]
                combo['values'] = department_values
            else:
                # بيانات افتراضية في حالة عدم وجود العمود
                combo['values'] = ("الإدارة الهندسية", "إدارة المشاريع", "إدارة الصيانة", "الإدارة المالية")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الإدارات: {e}")
            # بيانات افتراضية في حالة الخطأ
            combo['values'] = ("الإدارة الهندسية", "إدارة المشاريع", "إدارة الصيانة", "الإدارة المالية")
        finally:
            conn.close()

    def load_sections_data(self, combo):
        """تحميل قائمة الأقسام الفريدة من جدول المباني"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود عمود section_name
            cursor.execute("PRAGMA table_info(buildings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'section_name' in columns:
                cursor.execute('''
                    SELECT DISTINCT section_name
                    FROM buildings
                    WHERE section_name IS NOT NULL AND section_name != ''
                    ORDER BY section_name
                ''')
                sections = cursor.fetchall()
                section_values = [sect[0] for sect in sections]
                combo['values'] = section_values
            else:
                # بيانات افتراضية في حالة عدم وجود العمود
                combo['values'] = ("قسم الكهرباء", "قسم السباكة", "قسم التكييف", "قسم الصيانة العامة")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الأقسام: {e}")
            # بيانات افتراضية في حالة الخطأ
            combo['values'] = ("قسم الكهرباء", "قسم السباكة", "قسم التكييف", "قسم الصيانة العامة")
        finally:
            conn.close()

    def on_building_selected(self, event):
        """عند اختيار مبنى، تحديث حقلي الإدارة والقسم"""
        try:
            selected_building = self.entries["building_combo"].get()
            if selected_building and hasattr(self, 'buildings_data') and selected_building in self.buildings_data:
                building_info = self.buildings_data[selected_building]

                # تحديث حقل الإدارة (ComboBox)
                if "department_combo" in self.entries:
                    self.entries["department_combo"].set(building_info['department'])

                # تحديث حقل القسم (ComboBox)
                if "section_combo" in self.entries:
                    self.entries["section_combo"].set(building_info['section'])
        except Exception as e:
            print(f"خطأ في تحديث بيانات الإدارة والقسم: {e}")

    def create_form(self):
        """إنشاء نموذج بلاغ الصيانة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # تكوين الأعمدة للتخطيط
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=1)
        main_frame.columnconfigure(3, weight=1)

        # عنوان النموذج
        title_label = ttk_bs.Label(
            main_frame,
            text=f"🔧 {self.title} 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20), sticky="e")

        # الحقول مرتبة من اليمين لليسار
        labels = [
            ("📝 عنوان البلاغ *", "title_entry", "info"),
            ("🏢 المبنى", "building_combo", "info"),
            ("🏛️ اسم الإدارة", "department_combo", "primary"),
            ("🏢 اسم القسم", "section_combo", "primary"),
            ("🔧 نوع العطل", "type_combo", "warning"),
            ("⚡ الأولوية", "priority_combo", "danger"),
            ("📊 الحالة", "status_combo", "success"),
            ("👷 المكلف بالصيانة", "assigned_entry", "secondary"),
            ("📅 تاريخ البلاغ", "date_entry", "info"),
            ("💰 التكلفة المقدرة", "cost_entry", "warning"),
            ("📄 وصف المشكلة", "description_text", None),
            ("📝 ملاحظات إضافية", "notes_text", None)
        ]

        self.entries = {}

        # تخطيط الحقول من اليمين لليسار
        for idx, (label_text, attr_name, style) in enumerate(labels):
            row = (idx // 2) + 1
            # عكس ترتيب الأعمدة: الحقل الأول (idx=0) يذهب لليمين (col=2), الثاني (idx=1) لليسار (col=0)
            if idx % 2 == 0:  # الحقل الأول في الصف - يذهب لليمين
                col = 2
            else:  # الحقل الثاني في الصف - يذهب لليسار
                col = 0

            # التسمية على اليمين والحقل على اليسار
            label = ttk_bs.Label(main_frame, text=label_text)
            label.grid(row=row, column=col+1, sticky="e", pady=(0, 5), padx=(10, 15))

            if attr_name in ["description_text", "notes_text"]:
                # حقول النص تأخذ الصف كاملاً
                notes_label = ttk_bs.Label(main_frame, text=label_text)
                notes_label.grid(row=row, column=3, sticky="e", pady=(0, 5), padx=(10, 15), columnspan=1)

                text = tk.Text(main_frame, height=4 if attr_name == "description_text" else 3, width=120, wrap=tk.WORD)
                text.grid(row=row+1, column=0, columnspan=4, pady=(0, 20), padx=(15, 15), sticky="ew")
                self.entries[attr_name] = text
            elif attr_name.endswith("_combo"):
                combo = ttk_bs.Combobox(main_frame, width=50, bootstyle=style)
                if attr_name == "building_combo":
                    # تحميل المباني من قاعدة البيانات
                    self.load_buildings_data(combo)
                elif attr_name == "department_combo":
                    # تحميل الإدارات من قاعدة البيانات
                    self.load_departments_data(combo)
                elif attr_name == "section_combo":
                    # تحميل الأقسام من قاعدة البيانات
                    self.load_sections_data(combo)
                elif attr_name == "type_combo":
                    combo['values'] = ("تكييف", "سباكة", "كهرباء", "مصاعد", "تنظيف", "تقنية", "نجارة", "أخرى")
                elif attr_name == "priority_combo":
                    combo['values'] = ("عالية", "متوسطة", "منخفضة")
                elif attr_name == "status_combo":
                    combo['values'] = ("جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
                combo.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = combo

                # ربط حدث تغيير المبنى لتحديث الإدارة والقسم
                if attr_name == "building_combo":
                    combo.bind("<<ComboboxSelected>>", self.on_building_selected)
            elif attr_name == "date_entry":
                # استخدام منتقي التاريخ
                from datetime import datetime
                date_entry = ttk_bs.DateEntry(main_frame, dateformat="%Y-%m-%d", width=48, bootstyle=style)
                date_entry.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = date_entry
            else:
                entry = ttk_bs.Entry(main_frame, width=50, bootstyle=style)
                entry.grid(row=row, column=col, pady=(0, 15), padx=(30, 10), sticky="e")
                self.entries[attr_name] = entry

        # ملء البيانات إذا كان تعديل
        if self.request_data:
            self.entries["title_entry"].insert(0, self.request_data[1])
            # وصف المشكلة - إضافة وصف افتراضي إذا لم يكن موجود
            description = f"وصف المشكلة: {self.request_data[1]}" if len(self.request_data) <= 2 else (self.request_data[2] or "")
            self.entries["description_text"].insert('1.0', description)
            # المبنى
            building_id = str(self.request_data[2]) if len(self.request_data) > 2 else "غير محدد"
            self.entries["building_combo"].set(f"مبنى رقم {building_id}")
            # نوع العطل - استخدام الأولوية كنوع مؤقت
            self.entries["type_combo"].set("تكييف" if self.request_data[3] == "عالية" else "عام")
            # الأولوية - تحويل من الإنجليزية إلى العربية
            priority_ar = self.priority_en_to_ar.get(self.request_data[3], "متوسطة")
            self.entries["priority_combo"].set(priority_ar)
            # الحالة - تحويل من الإنجليزية إلى العربية
            status_ar = self.status_en_to_ar.get(self.request_data[4], "جديد")
            self.entries["status_combo"].set(status_ar)
            # المكلف
            self.entries["assigned_entry"].insert(0, self.request_data[6] if len(self.request_data) > 6 else "")
            # التاريخ
            if len(self.request_data) > 7 and self.request_data[7]:
                try:
                    # تحويل التاريخ إلى تنسيق مناسب لمنتقي التاريخ
                    date_str = str(self.request_data[7])
                    if '/' in date_str:
                        # تحويل من تنسيق YYYY/MM/DD إلى YYYY-MM-DD
                        date_str = date_str.split(' ')[0].replace('/', '-')
                    elif ' ' in date_str:
                        # أخذ الجزء الأول فقط (التاريخ بدون الوقت)
                        date_str = date_str.split(' ')[0]

                    self.entries["date_entry"].entry.delete(0, tk.END)
                    self.entries["date_entry"].entry.insert(0, date_str)
                except:
                    # في حالة الخطأ، استخدام التاريخ الحالي
                    from datetime import datetime
                    today = datetime.now().strftime("%Y-%m-%d")
                    self.entries["date_entry"].entry.delete(0, tk.END)
                    self.entries["date_entry"].entry.insert(0, today)
            # التكلفة - قيمة افتراضية
            self.entries["cost_entry"].insert(0, "0")
            # الملاحظات
            notes = self.request_data[8] if len(self.request_data) > 8 else ""
            self.entries["notes_text"].insert('1.0', notes)

            # الإدارة والقسم (إذا كانت متوفرة في البيانات)
            if len(self.request_data) > 9:  # department_name
                department = self.request_data[9] if self.request_data[9] else ""
                self.entries["department_combo"].set(department)

            if len(self.request_data) > 10:  # section_name
                section = self.request_data[10] if self.request_data[10] else ""
                self.entries["section_combo"].set(section)

        # الأزرار
        # حساب الصف التالي بعد آخر حقل (مع مراعاة أن حقول النص تأخذ صفين إضافيين)
        last_row = ((len(labels) - 1) // 2) + 4  # +4 لأن حقول النص تأخذ صفين إضافيين
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.grid(row=last_row, column=0, columnspan=4, pady=30, sticky="ew")

        # توسيط الأزرار
        button_container = ttk_bs.Frame(button_frame)
        button_container.pack(expand=True)

        # ترتيب الأزرار من اليمين لليسار
        self.cancel_button = ttk_bs.Button(
            button_container,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18)
        self.cancel_button.pack(side=tk.RIGHT, padx=10, ipady=12)

        self.save_button = ttk_bs.Button(
            button_container,
            text="💾 حفظ",
            command=self.save_request,
            bootstyle="success",
            width=18)
        self.save_button.pack(side=tk.RIGHT, padx=10, ipady=12)
    
    def save_request(self):
        """حفظ بيانات بلاغ الصيانة"""
        try:
            title = self.entries["title_entry"].get().strip()
            description = self.entries["description_text"].get('1.0', tk.END).strip()
            building = self.entries["building_combo"].get().strip()
            department = self.entries["department_combo"].get().strip()
            section = self.entries["section_combo"].get().strip()
            priority = self.entries["priority_combo"].get().strip()
            status = self.entries["status_combo"].get().strip()
            assigned_to = self.entries["assigned_entry"].get().strip()
            notes = self.entries["notes_text"].get('1.0', tk.END).strip()

            # قراءة التاريخ من منتقي التاريخ
            try:
                selected_date = self.entries["date_entry"].entry.get().strip()
                if not selected_date:
                    # استخدام التاريخ الحالي إذا لم يتم اختيار تاريخ
                    from datetime import datetime
                    selected_date = datetime.now().strftime("%Y-%m-%d")
            except:
                # في حالة الخطأ، استخدام التاريخ الحالي
                from datetime import datetime
                selected_date = datetime.now().strftime("%Y-%m-%d")
        except KeyError as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول إلى الحقل: {e}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة البيانات: {e}")
            return

        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان البلاغ")
            return

        # تحويل معرف المبنى إلى رقم أو NULL
        building_id = None
        if building and building != "غير محدد":
            try:
                # استخراج الرقم من النص مثل "1 - المبنى الإداري"
                if " - " in building:
                    building_id = int(building.split(" - ")[0])
                else:
                    # محاولة استخراج أي رقم من النص
                    import re
                    match = re.search(r'\d+', building)
                    if match:
                        building_id = int(match.group())
                    else:
                        building_id = 1  # قيمة افتراضية
            except:
                building_id = 1  # قيمة افتراضية في حالة الخطأ

        # تحويل assigned_to إلى رقم أو NULL
        assigned_to_id = None
        if assigned_to and assigned_to.strip():
            try:
                assigned_to_id = int(assigned_to) if assigned_to.isdigit() else None
            except:
                assigned_to_id = None

        # تحويل الأولوية والحالة من العربية إلى الإنجليزية
        priority_en = self.priority_ar_to_en.get(priority, "medium")
        status_en = self.status_ar_to_en.get(status, "open")

        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            # أولاً، التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
            try:
                cursor.execute("PRAGMA table_info(maintenance_requests)")
                columns = [column[1] for column in cursor.fetchall()]

                if 'department_name' not in columns:
                    cursor.execute("ALTER TABLE maintenance_requests ADD COLUMN department_name TEXT")
                if 'section_name' not in columns:
                    cursor.execute("ALTER TABLE maintenance_requests ADD COLUMN section_name TEXT")

                conn.commit()
            except Exception as e:
                print(f"تحذير: لم يتم إضافة الأعمدة الجديدة: {e}")

            if self.request_data:  # تعديل
                cursor.execute('''
                    UPDATE maintenance_requests
                    SET title=?, description=?, building_id=?, department_name=?, section_name=?,
                        priority=?, status=?, assigned_to=?, notes=?
                    WHERE id=?
                ''', (title, description, building_id, department, section, priority_en, status_en,
                      assigned_to_id, notes, self.request_data[0]))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO maintenance_requests (title, description, building_id, department_name, section_name,
                                                    priority, status, reported_by, assigned_to, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (title, description, building_id, department, section, priority_en, status_en,
                      self.auth_manager.current_user.get('username', 'مجهول'), assigned_to_id, notes))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بلاغ الصيانة بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()

    def print_maintenance_report(self):
        """طباعة تقرير الصيانة والأعطال إلى PDF"""
        try:
            # التحقق من وجود مكتبة reportlab
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import cm
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
                from reportlab.lib import colors
                from reportlab.lib.enums import TA_CENTER, TA_RIGHT
                from datetime import datetime
                import os
                from tkinter import filedialog
            except ImportError:
                messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها أولاً:\npip install reportlab")
                return

            # الحصول على بيانات الصيانة
            maintenance_data = self.get_maintenance_data_for_report()

            if not maintenance_data:
                messagebox.showwarning("تحذير", "لا توجد بلاغات صيانة لطباعتها")
                return

            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_الصيانة_{current_date}.pdf"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                initialfile=default_filename,
                title="حفظ تقرير الصيانة"
            )

            if not file_path:
                return

            # إنشاء ملف PDF
            self.create_maintenance_pdf_report(maintenance_data, file_path)

            messagebox.showinfo("نجح", f"تم إنشاء تقرير الصيانة بنجاح:\n{file_path}")

            # فتح الملف للطباعة بطريقة آمنة
            try:
                import platform
                import subprocess

                system = platform.system()
                if system == "Windows":
                    # استخدام طريقة آمنة لفتح الملف
                    try:
                        # محاولة فتح الملف مع برنامج PDF الافتراضي
                        subprocess.run(['start', '', file_path], shell=True, check=False)
                    except:
                        # في حالة فشل الطريقة الأولى، استخدم os.startfile
                        try:
                            os.startfile(file_path)
                        except:
                            # إذا فشلت جميع الطرق، أظهر رسالة للمستخدم
                            messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', file_path], check=False)
                elif system == "Linux":
                    subprocess.run(['xdg-open', file_path], check=False)
                else:
                    messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")
            except Exception as e:
                messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{file_path}\nيرجى فتحه يدوياً للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير: {str(e)}")

    def get_maintenance_data_for_report(self):
        """الحصول على بيانات الصيانة للتقرير"""
        # بيانات وهمية لبلاغات الصيانة (يمكن استبدالها ببيانات حقيقية من قاعدة البيانات)
        sample_maintenance = [
            {
                'title': 'عطل في نظام التكييف',
                'description': 'توقف مكيف الهواء في القاعة الرئيسية',
                'building': 'المبنى الإداري',
                'priority': 'عالية',
                'status': 'قيد المعالجة',
                'reported_date': '2025-07-01',
                'assigned_to': 'فني التكييف',
                'notes': 'يحتاج إلى قطع غيار'
            },
            {
                'title': 'مشكلة في الإضاءة',
                'description': 'عدم عمل الإضاءة في الممر الشرقي',
                'building': 'مبنى الهندسة',
                'priority': 'متوسطة',
                'status': 'مكتمل',
                'reported_date': '2025-06-28',
                'assigned_to': 'فني الكهرباء',
                'notes': 'تم استبدال المصابيح'
            },
            {
                'title': 'تسريب في السباكة',
                'description': 'تسريب مياه في دورة المياه الطابق الثاني',
                'building': 'مبنى المختبرات',
                'priority': 'عالية',
                'status': 'جديد',
                'reported_date': '2025-07-02',
                'assigned_to': 'فني السباكة',
                'notes': 'يحتاج إلى تدخل عاجل'
            },
            {
                'title': 'صيانة دورية للمصاعد',
                'description': 'فحص وصيانة دورية لجميع المصاعد',
                'building': 'جميع المباني',
                'priority': 'منخفضة',
                'status': 'مجدول',
                'reported_date': '2025-07-03',
                'assigned_to': 'شركة المصاعد',
                'notes': 'صيانة شهرية مجدولة'
            },
            {
                'title': 'إصلاح الباب الرئيسي',
                'description': 'مشكلة في آلية إغلاق الباب الرئيسي',
                'building': 'المبنى الإداري',
                'priority': 'متوسطة',
                'status': 'قيد المعالجة',
                'reported_date': '2025-06-30',
                'assigned_to': 'فني الأقفال',
                'notes': 'في انتظار قطع الغيار'
            }
        ]

        return sample_maintenance

    def create_maintenance_pdf_report(self, maintenance_data, file_path):
        """إنشاء تقرير PDF احترافي أفقي للصيانة مع دعم كامل للعربية"""
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
        from datetime import datetime

        # إعداد الخطوط العربية المحسنة
        self.setup_arabic_pdf_support()

        # إنشاء مستند PDF بتخطيط أفقي محسن للطباعة
        doc = SimpleDocTemplate(
            file_path,
            pagesize=landscape(A4),  # تخطيط أفقي
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm,
            title="تقرير الصيانة والأعطال",
            author="نظام إدارة أعمال الإدارة الهندسية",
            subject="تقرير إداري أفقي - الصيانة والأعطال",
            creator="Engineering Management System - Maintenance Landscape Report"
        )

        # إنشاء الأنماط المحسنة للتخطيط الأفقي
        styles = getSampleStyleSheet()

        # تحديد الخطوط المناسبة للتخطيط الأفقي
        if hasattr(self, 'registered_fonts') and self.registered_fonts:
            title_font = self.registered_fonts.get('Arabic-Bold', 'Helvetica-Bold')
            heading_font = self.registered_fonts.get('Arabic-Bold', 'Helvetica-Bold')
            normal_font = self.registered_fonts.get('Arabic-Regular', 'Helvetica')
        else:
            title_font = heading_font = 'Helvetica-Bold'
            normal_font = 'Helvetica'

        # أنماط احترافية محسنة للتخطيط الأفقي
        title_style = ParagraphStyle(
            'LandscapeTitle',
            parent=styles['Title'],
            fontSize=24,  # خط أكبر للتخطيط الأفقي
            spaceAfter=25,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.58),  # أزرق داكن احترافي
            fontName=title_font,
            backColor=colors.Color(0.95, 0.95, 0.95),  # خلفية رمادية فاتحة
            borderWidth=2,
            borderColor=colors.Color(0.12, 0.23, 0.58),
            leftIndent=10,
            rightIndent=10,
            topPadding=10,
            bottomPadding=10
        )

        heading_style = ParagraphStyle(
            'LandscapeHeading',
            parent=styles['Heading1'],
            fontSize=16,  # خط أكبر للعناوين الفرعية
            spaceAfter=15,
            alignment=TA_RIGHT,
            textColor=colors.Color(0.0, 0.5, 0.0),  # أخضر داكن
            fontName=heading_font,
            backColor=colors.Color(0.9, 1.0, 0.9),  # خلفية خضراء فاتحة
            leftIndent=5,
            rightIndent=5,
            topPadding=5,
            bottomPadding=5
        )

        # نمط النصوص العادية المحسن
        normal_style = ParagraphStyle(
            'LandscapeNormal',
            parent=styles['Normal'],
            fontSize=12,  # خط أكبر للنصوص العادية
            spaceAfter=8,
            alignment=TA_RIGHT,
            fontName=normal_font,
            leading=16  # تباعد أكبر بين الأسطر
        )

        # نمط التاريخ
        date_style = ParagraphStyle(
            'LandscapeDate',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            alignment=TA_LEFT,
            fontName=normal_font,
            textColor=colors.Color(0.4, 0.4, 0.4)  # رمادي
        )

        # قائمة العناصر
        story = []

        # العنوان الرئيسي مع تصميم احترافي
        story.append(Paragraph("🔧 تقرير الصيانة والأعطال - تخطيط أفقي محسن 🔧", title_style))
        story.append(Spacer(1, 25))

        # التاريخ مع تنسيق محسن
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        story.append(Paragraph(f"📅 تاريخ إنشاء التقرير: {current_time}", date_style))
        story.append(Spacer(1, 20))

        # إحصائيات سريعة مع تصميم محسن
        total_requests = len(maintenance_data)
        completed_requests = len([r for r in maintenance_data if r['status'] == 'مكتمل'])
        pending_requests = len([r for r in maintenance_data if r['status'] in ['جديد', 'قيد المعالجة']])
        high_priority = len([r for r in maintenance_data if r['priority'] == 'عالية'])

        story.append(Paragraph("📊 الإحصائيات العامة للصيانة والأعطال", heading_style))
        story.append(Spacer(1, 10))
        story.append(Paragraph(f"• إجمالي بلاغات الصيانة: {total_requests} بلاغ", normal_style))
        story.append(Paragraph(f"• البلاغات المكتملة: {completed_requests} بلاغ ({completed_requests/total_requests*100:.1f}%)" if total_requests > 0 else "• البلاغات المكتملة: 0 بلاغ", normal_style))
        story.append(Paragraph(f"• البلاغات المعلقة: {pending_requests} بلاغ ({pending_requests/total_requests*100:.1f}%)" if total_requests > 0 else "• البلاغات المعلقة: 0 بلاغ", normal_style))
        story.append(Paragraph(f"• البلاغات عالية الأولوية: {high_priority} بلاغ ({high_priority/total_requests*100:.1f}%)" if total_requests > 0 else "• البلاغات عالية الأولوية: 0 بلاغ", normal_style))
        story.append(Spacer(1, 25))

        # جدول بلاغات الصيانة مع تصميم احترافي أفقي
        story.append(Paragraph("📋 جدول تفاصيل بلاغات الصيانة والأعطال", heading_style))
        story.append(Spacer(1, 15))

        # إنشاء بيانات الجدول مع أعمدة محسنة للتخطيط الأفقي
        table_data = [['عنوان البلاغ', 'المبنى', 'الأولوية', 'الحالة', 'تاريخ البلاغ', 'المسؤول', 'الملاحظات']]

        for request in maintenance_data:
            # تحديد لون الحالة
            status = request['status']
            priority = request['priority']

            table_data.append([
                request['title'][:25] + '...' if len(request['title']) > 25 else request['title'],
                request['building'],
                priority,
                status,
                request.get('reported_date', 'غير محدد'),
                request['assigned_to'],
                request.get('notes', 'لا توجد ملاحظات')[:20] + '...' if len(request.get('notes', '')) > 20 else request.get('notes', 'لا توجد ملاحظات')
            ])

        # إنشاء الجدول مع عرض أعمدة محسن للتخطيط الأفقي
        col_widths = [4*cm, 2.5*cm, 2*cm, 2*cm, 2*cm, 2.5*cm, 3*cm]  # عرض مناسب للتخطيط الأفقي
        table = Table(table_data, colWidths=col_widths, repeatRows=1)

        # تطبيق تصميم احترافي محسن
        table.setStyle(TableStyle([
            # تنسيق الرأس - أزرق داكن احترافي
            ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.12, 0.23, 0.58)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), heading_font),
            ('FONTSIZE', (0, 0), (-1, 0), 12),  # خط أكبر للرؤوس
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            ('TOPPADDING', (0, 0), (-1, 0), 15),

            # تنسيق البيانات - خط أكبر وتباعد محسن
            ('FONTNAME', (0, 1), (-1, -1), normal_font),
            ('FONTSIZE', (0, 1), (-1, -1), 11),  # خط أكبر للبيانات
            ('GRID', (0, 0), (-1, -1), 1.5, colors.Color(0.3, 0.3, 0.3)),  # حدود أوضح
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),  # صفوف متناوبة
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 10),

            # تمييز لوني للحالات والأولويات
            ('TEXTCOLOR', (3, 1), (3, -1), colors.Color(0.0, 0.6, 0.0)),  # أخضر للحالة المكتملة
            ('TEXTCOLOR', (2, 1), (2, -1), colors.Color(0.8, 0.0, 0.0)),  # أحمر للأولوية العالية
        ]))

        story.append(table)
        story.append(Spacer(1, 30))

        # إضافة تذييل احترافي
        footer_style = ParagraphStyle(
            'LandscapeFooter',
            parent=styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            fontName=normal_font,
            textColor=colors.Color(0.4, 0.4, 0.4),
            spaceAfter=10
        )

        story.append(Paragraph("=" * 100, footer_style))
        story.append(Spacer(1, 10))
        story.append(Paragraph("🏗️ نظام إدارة أعمال الإدارة الهندسية - تقرير الصيانة والأعطال", footer_style))
        story.append(Paragraph("Engineering Management System - Maintenance & Faults Report", footer_style))
        story.append(Paragraph(f"تم إنشاء هذا التقرير في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", footer_style))

        # بناء المستند مع معالجة الأخطاء
        try:
            doc.build(story, onFirstPage=self.add_page_number, onLaterPages=self.add_page_number)

            # فتح نافذة الطباعة تلقائياً (اختياري)
            self.auto_print_pdf(file_path)

        except Exception as e:
            raise Exception(f"فشل في بناء مستند PDF: {str(e)}")

    def add_page_number(self, canvas, doc):
        """إضافة رقم الصفحة"""
        try:
            from reportlab.lib.units import cm
            canvas.saveState()
            canvas.setFont('Helvetica', 10)
            page_num = canvas.getPageNumber()
            text = f"صفحة {page_num}"
            canvas.drawRightString(doc.pagesize[0] - 2*cm, 1*cm, text)
            canvas.restoreState()
        except:
            pass

    def auto_print_pdf(self, file_path):
        """فتح نافذة الطباعة تلقائياً"""
        try:
            import platform
            import subprocess
            import os

            if os.path.exists(file_path):
                system = platform.system()
                if system == "Windows":
                    # فتح الملف مع برنامج PDF الافتراضي
                    subprocess.run(['start', '', file_path], shell=True, check=False)
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', file_path], check=False)
                elif system == "Linux":
                    subprocess.run(['xdg-open', file_path], check=False)
        except Exception as e:
            print(f"تحذير: لم يتم فتح الملف تلقائياً: {e}")
