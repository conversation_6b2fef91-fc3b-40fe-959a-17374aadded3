# 🔧 تحديث حقول الإدارة والقسم في شاشة إضافة بلاغ صيانة جديد

## 📋 الطلب
تحويل حقلي "اسم الإدارة" و "اسم القسم" في شاشة إضافة بلاغ صيانة جديد من حقول نص عادية إلى قوائم منسدلة تسحب البيانات من شاشة إضافة مبنى جديد.

## ✅ التحديثات المطبقة

### 1. إضافة دوال تحميل البيانات

#### دالة تحميل الإدارات:
```python
def load_departments_data(self, combo):
    """تحميل قائمة الإدارات الفريدة من جدول المباني"""
    try:
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود عمود department_name
        cursor.execute("PRAGMA table_info(buildings)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'department_name' in columns:
            cursor.execute('''
                SELECT DISTINCT department_name
                FROM buildings
                WHERE department_name IS NOT NULL AND department_name != ''
                ORDER BY department_name
            ''')
            departments = cursor.fetchall()
            department_values = [dept[0] for dept in departments]
            combo['values'] = department_values
        else:
            # بيانات افتراضية في حالة عدم وجود العمود
            combo['values'] = ("الإدارة الهندسية", "إدارة المشاريع", "إدارة الصيانة", "الإدارة المالية")

    except Exception as e:
        print(f"خطأ في تحميل بيانات الإدارات: {e}")
        # بيانات افتراضية في حالة الخطأ
        combo['values'] = ("الإدارة الهندسية", "إدارة المشاريع", "إدارة الصيانة", "الإدارة المالية")
    finally:
        conn.close()
```

#### دالة تحميل الأقسام:
```python
def load_sections_data(self, combo):
    """تحميل قائمة الأقسام الفريدة من جدول المباني"""
    try:
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود عمود section_name
        cursor.execute("PRAGMA table_info(buildings)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'section_name' in columns:
            cursor.execute('''
                SELECT DISTINCT section_name
                FROM buildings
                WHERE section_name IS NOT NULL AND section_name != ''
                ORDER BY section_name
            ''')
            sections = cursor.fetchall()
            section_values = [sect[0] for sect in sections]
            combo['values'] = section_values
        else:
            # بيانات افتراضية في حالة عدم وجود العمود
            combo['values'] = ("قسم الكهرباء", "قسم السباكة", "قسم التكييف", "قسم الصيانة العامة")

    except Exception as e:
        print(f"خطأ في تحميل بيانات الأقسام: {e}")
        # بيانات افتراضية في حالة الخطأ
        combo['values'] = ("قسم الكهرباء", "قسم السباكة", "قسم التكييف", "قسم الصيانة العامة")
    finally:
        conn.close()
```

### 2. تحديث قائمة الحقول
تم تغيير نوع الحقول من `entry` إلى `combo`:
```python
labels = [
    ("📝 عنوان البلاغ *", "title_entry", "info"),
    ("🏢 المبنى", "building_combo", "info"),
    ("🏛️ اسم الإدارة", "department_combo", "primary"),  # ← تم التغيير من department_entry
    ("🏢 اسم القسم", "section_combo", "primary"),        # ← تم التغيير من section_entry
    ("🔧 نوع العطل", "type_combo", "warning"),
    # ... باقي الحقول
]
```

### 3. تحديث إنشاء الحقول
تم إضافة تحميل البيانات للقوائم المنسدلة الجديدة:
```python
elif attr_name.endswith("_combo"):
    combo = ttk_bs.Combobox(main_frame, width=50, bootstyle=style)
    if attr_name == "building_combo":
        # تحميل المباني من قاعدة البيانات
        self.load_buildings_data(combo)
    elif attr_name == "department_combo":
        # تحميل الإدارات من قاعدة البيانات
        self.load_departments_data(combo)
    elif attr_name == "section_combo":
        # تحميل الأقسام من قاعدة البيانات
        self.load_sections_data(combo)
    # ... باقي القوائم المنسدلة
```

### 4. تحديث دالة اختيار المبنى
تم تحديث الدالة للتعامل مع القوائم المنسدلة:
```python
def on_building_selected(self, event):
    """عند اختيار مبنى، تحديث حقلي الإدارة والقسم"""
    try:
        selected_building = self.entries["building_combo"].get()
        if selected_building and hasattr(self, 'buildings_data') and selected_building in self.buildings_data:
            building_info = self.buildings_data[selected_building]

            # تحديث حقل الإدارة (ComboBox)
            if "department_combo" in self.entries:
                self.entries["department_combo"].set(building_info['department'])

            # تحديث حقل القسم (ComboBox)
            if "section_combo" in self.entries:
                self.entries["section_combo"].set(building_info['section'])
    except Exception as e:
        print(f"خطأ في تحديث بيانات الإدارة والقسم: {e}")
```

### 5. تحديث دالة الحفظ
تم تحديث قراءة البيانات من القوائم المنسدلة:
```python
def save_request(self):
    """حفظ بيانات بلاغ الصيانة"""
    try:
        title = self.entries["title_entry"].get().strip()
        description = self.entries["description_text"].get('1.0', tk.END).strip()
        building = self.entries["building_combo"].get().strip()
        department = self.entries["department_combo"].get().strip()  # ← تم التغيير
        section = self.entries["section_combo"].get().strip()        # ← تم التغيير
        # ... باقي الحقول
```

### 6. تحديث تحميل البيانات للتعديل
تم إضافة تحميل بيانات الإدارة والقسم عند التعديل:
```python
# الإدارة والقسم (إذا كانت متوفرة في البيانات)
if len(self.request_data) > 9:  # department_name
    department = self.request_data[9] if self.request_data[9] else ""
    self.entries["department_combo"].set(department)

if len(self.request_data) > 10:  # section_name
    section = self.request_data[10] if self.request_data[10] else ""
    self.entries["section_combo"].set(section)
```

### 7. تحديث استعلام تحميل البيانات
تم تحديث الاستعلام ليشمل الإدارة والقسم:
```python
# التحقق من وجود الأعمدة الجديدة أولاً
cursor.execute("PRAGMA table_info(maintenance_requests)")
columns = [column[1] for column in cursor.fetchall()]

if 'department_name' in columns and 'section_name' in columns:
    cursor.execute('''
        SELECT id, title, building_id, priority, status, reported_by,
               assigned_to, created_at, notes, department_name, section_name
        FROM maintenance_requests
        ORDER BY created_at DESC
    ''')
else:
    cursor.execute('''
        SELECT id, title, building_id, priority, status, reported_by,
               assigned_to, created_at, notes
        FROM maintenance_requests
        ORDER BY created_at DESC
    ''')
```

## 🎯 النتائج

### ✅ الميزات الجديدة:
1. **قائمة منسدلة للإدارات**: تعرض جميع الإدارات الفريدة من جدول المباني
2. **قائمة منسدلة للأقسام**: تعرض جميع الأقسام الفريدة من جدول المباني
3. **تحديث تلقائي**: عند اختيار مبنى، يتم تحديث الإدارة والقسم تلقائياً
4. **حفظ محسن**: يتم حفظ بيانات الإدارة والقسم في قاعدة البيانات
5. **تحميل للتعديل**: يتم تحميل بيانات الإدارة والقسم عند تعديل بلاغ موجود

### 🔧 التحسينات التقنية:
1. **إضافة أعمدة تلقائية**: يتم إضافة أعمدة `department_name` و `section_name` تلقائياً لجدول `maintenance_requests`
2. **معالجة الأخطاء**: معالجة شاملة للأخطاء مع بيانات افتراضية
3. **توافق مع البيانات الموجودة**: يعمل مع قواعد البيانات الموجودة والجديدة
4. **واجهة متسقة**: تصميم متسق مع باقي القوائم المنسدلة في النظام

## 📁 الملفات المعدلة
- `maintenance_management.py`: تم تحديث فئة `MaintenanceDialog`

## 🧪 الاختبار
تم اختبار التحديثات والتأكد من:
- ✅ تحميل القوائم المنسدلة بالبيانات الصحيحة
- ✅ التحديث التلقائي عند اختيار المبنى
- ✅ حفظ البيانات بنجاح
- ✅ تحميل البيانات للتعديل
- ✅ التوافق مع البيانات الموجودة

🎉 **تم تنفيذ الطلب بنجاح! الآن حقلا اسم الإدارة واسم القسم في شاشة إضافة بلاغ صيانة جديد أصبحا قوائم منسدلة تسحب البيانات من شاشة إضافة مبنى جديد.**
